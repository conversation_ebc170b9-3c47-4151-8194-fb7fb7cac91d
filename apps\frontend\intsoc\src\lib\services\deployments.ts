import { Deployment, DeploymentsResponse } from '@telesoft/types';
import { apiClient } from '../api-client';
import { getGlobalConfig } from '../contexts/config-provider';

export interface DeploymentsWebSocketMessage {
  type:
    | 'ml-update'
    | 'ml-initial'
    | 'ping'
    | 'pong'
    | 'subscribe'
    | 'unsubscribe';
  data?: {
    deployments?: Deployment[];
    action?: 'append' | 'update' | 'clear'; // Support multiple actions for different operations
  };
  timestamp: string;
}

/**
 * Service for managing machine learning deployments data
 */
export class DeploymentsService {
  private wsConnection: WebSocket | null = null;
  private wsUrl: string | null = null;
  private reconnectTimeoutId: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private currentReconnectInterval = 5000; // Start with 5 seconds
  private maxReconnectInterval = 30000; // Cap at 30 seconds
  private shouldReconnect = false;

  // Subscription management for multiple consumers
  private subscribers: Map<
    string,
    {
      onMessage: (message: DeploymentsWebSocketMessage) => void;
      onError?: (error: Event) => void;
      onClose?: (event: CloseEvent) => void;
      onOpen?: () => void;
    }
  > = new Map();

  private nextSubscriberId = 1;

  /**
   * Get WebSocket URL for deployments
   * Note: WebSocket connections are ALWAYS direct to backend (no Next.js proxy)
   * Always gets fresh URL from current config to handle runtime config changes
   */
  private getWebSocketUrl(): string {
    const config = getGlobalConfig().api;
    // WebSocket connections bypass Next.js and connect directly to backend
    const wsUrl = config.wsUrl.replace(/^http/, 'ws');
    const fullWsUrl = `${wsUrl}/ml`;

    return fullWsUrl;
  }

  /**
   * Connect to WebSocket for real-time deployment updates with automatic reconnection
   * First fetches initial data over HTTP, then establishes WebSocket for incremental updates
   *
   * NOTE: This service enforces a single WebSocket connection. Multiple calls will reuse
   * the existing connection and return a subscription ID for managing multiple consumers.
   */
  async connectWebSocket(
    onMessage: (message: DeploymentsWebSocketMessage) => void,
    onError?: (error: Event) => void,
    onClose?: (event: CloseEvent) => void,
    onOpen?: () => void,
  ): Promise<{
    ws: WebSocket;
    initialData: Deployment[];
    subscriptionId: string;
  }> {
    // Generate a unique subscription ID
    const subscriptionId = `sub_${this.nextSubscriberId++}`;

    // Store subscriber callbacks
    this.subscribers.set(subscriptionId, {
      onMessage,
      onError,
      onClose,
      onOpen,
    });

    console.log(
      `Deployments WebSocket: Adding subscriber ${subscriptionId}. Total subscribers: ${this.subscribers.size}`,
      `Connection state: ${this.wsConnection?.readyState}`,
    );

    // First, fetch initial data over HTTP to avoid WebSocket message size limits
    console.log('Fetching initial deployment data over HTTP...');
    const initialData = await this.getDeployments();

    // Send initial data through the message callback
    const initialMessage: DeploymentsWebSocketMessage = {
      type: 'ml-initial',
      data: {
        deployments: initialData,
      },
      timestamp: new Date().toISOString(),
    };
    onMessage(initialMessage);

    // If we already have an active connection, just notify the new subscriber and return
    if (this.wsConnection?.readyState === WebSocket.OPEN) {
      console.log(
        `Deployments WebSocket: Reusing existing connection for subscriber ${subscriptionId}`,
      );

      // Important: Notify the new subscriber immediately that connection is open
      // This ensures the hook state is properly updated
      setTimeout(() => {
        onOpen?.();
      }, 0);

      return { ws: this.wsConnection, initialData, subscriptionId };
    }

    // If connection is in progress, just return (the subscriber will be notified when it opens)
    if (this.wsConnection?.readyState === WebSocket.CONNECTING) {
      console.log(
        `Deployments WebSocket: Connection in progress, subscriber ${subscriptionId} will be notified when ready`,
      );
      return { ws: this.wsConnection, initialData, subscriptionId };
    }

    // Create new connection
    this.shouldReconnect = true;
    console.log('Creating new WebSocket connection for deployments');
    const ws = this.establishConnection();

    return { ws, initialData, subscriptionId };
  }

  /**
   * Disconnect a specific subscriber from WebSocket updates
   */
  disconnectSubscriber(subscriptionId: string): void {
    const removed = this.subscribers.delete(subscriptionId);
    if (removed) {
      console.log(
        `Deployments WebSocket: Removed subscriber ${subscriptionId}. Remaining subscribers: ${this.subscribers.size}`,
      );
    }

    // If no more subscribers, disconnect the WebSocket
    if (this.subscribers.size === 0) {
      console.log(
        'Deployments WebSocket: No more subscribers, disconnecting...',
      );
      this.disconnectWebSocket();
    }
  }

  /**
   * Internal method to establish WebSocket connection
   */
  private establishConnection(): WebSocket {
    if (this.wsConnection?.readyState === WebSocket.OPEN) {
      return this.wsConnection;
    }

    // Clear any existing reconnect timeout
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }

    const ws = new WebSocket(this.getWebSocketUrl());
    this.wsConnection = ws;

    ws.onopen = () => {
      console.log('Deployments WebSocket connected');
      this.reconnectAttempts = 0;
      this.currentReconnectInterval = 5000; // Reset interval on successful connection

      // Subscribe to deployment updates
      const subscribeMessage: DeploymentsWebSocketMessage = {
        type: 'subscribe',
        timestamp: new Date().toISOString(),
      };
      ws.send(JSON.stringify(subscribeMessage));

      // Notify all subscribers that connection is open
      this.subscribers.forEach((subscriber) => subscriber.onOpen?.());
    };

    ws.onmessage = (event) => {
      try {
        const message: DeploymentsWebSocketMessage = JSON.parse(event.data);
        // Broadcast message to all subscribers
        this.subscribers.forEach((subscriber) => subscriber.onMessage(message));
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('Deployments WebSocket error:', error);
      // Notify all subscribers of error
      this.subscribers.forEach((subscriber) => subscriber.onError?.(error));
    };

    ws.onclose = (event) => {
      console.log('Deployments WebSocket closed:', event.code, event.reason);
      this.wsConnection = null;
      // Notify all subscribers of close
      this.subscribers.forEach((subscriber) => subscriber.onClose?.(event));

      // Only attempt reconnection if it wasn't a clean close and we should reconnect
      this.attemptReconnection();
    };

    return ws;
  }

  /**
   * Attempt to reconnect with exponential backoff
   */
  private attemptReconnection(): void {
    // Only reconnect if we have subscribers and should reconnect
    if (!this.shouldReconnect || this.subscribers.size === 0) {
      console.log(
        'Deployments WebSocket: Not reconnecting (no subscribers or disabled)',
      );
      return;
    }

    this.reconnectAttempts++;

    // Calculate exponential backoff with jitter
    const exponentialDelay = Math.min(
      this.currentReconnectInterval *
        Math.pow(2, Math.min(this.reconnectAttempts - 1, 6)),
      this.maxReconnectInterval,
    );
    const jitter = Math.random() * 1000; // Add up to 1 second of jitter
    const delay = exponentialDelay + jitter;

    this.currentReconnectInterval = delay;

    console.log(
      `Deployments WebSocket disconnected. Attempting to reconnect in ${Math.round(delay)}ms (attempt ${this.reconnectAttempts})...`,
    );

    this.reconnectTimeoutId = setTimeout(async () => {
      if (this.shouldReconnect && this.subscribers.size > 0) {
        try {
          // Fetch fresh initial data on reconnection
          console.log('Fetching fresh deployment data on reconnection...');
          const initialData = await this.getDeployments();

          // Send initial data to all subscribers
          const initialMessage: DeploymentsWebSocketMessage = {
            type: 'ml-initial',
            data: {
              deployments: initialData,
            },
            timestamp: new Date().toISOString(),
          };
          this.subscribers.forEach((subscriber) =>
            subscriber.onMessage(initialMessage),
          );

          // Then re-establish WebSocket connection
          this.establishConnection();
        } catch (error) {
          console.error('Failed to fetch initial data on reconnection:', error);
          // Continue with WebSocket connection even if HTTP fetch fails
          this.establishConnection();
        }
      }
    }, delay);
  }

  /**
   * Disconnect from WebSocket and stop reconnection attempts
   */
  disconnectWebSocket(): void {
    this.shouldReconnect = false;

    // Clear reconnection timeout
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }

    // Close connection
    if (this.wsConnection) {
      // Send unsubscribe message before closing
      if (this.wsConnection.readyState === WebSocket.OPEN) {
        const unsubscribeMessage: DeploymentsWebSocketMessage = {
          type: 'unsubscribe',
          timestamp: new Date().toISOString(),
        };
        this.wsConnection.send(JSON.stringify(unsubscribeMessage));
      }

      this.wsConnection.close();
      this.wsConnection = null;
    }

    // Clear all subscribers
    this.subscribers.clear();

    console.log('Deployments WebSocket disconnected and subscribers cleared');
  }

  /**
   * Send a message through the WebSocket connection
   */
  sendWebSocketMessage(message: DeploymentsWebSocketMessage): boolean {
    if (this.wsConnection?.readyState === WebSocket.OPEN) {
      this.wsConnection.send(JSON.stringify(message));
      return true;
    }
    return false;
  }

  /**
   * Check if WebSocket is connected
   */
  isWebSocketConnected(): boolean {
    return this.wsConnection?.readyState === WebSocket.OPEN;
  }

  /**
   * Fetch all deployment data from the backend API
   */
  async getDeployments(): Promise<Deployment[]> {
    try {
      const response = await apiClient.get<DeploymentsResponse>(
        '/api/v1/machine-learning/deployments',
        {
          skipCache: true, // Force skip cache for deployments data
        },
      );
      return response.data.deployments;
    } catch (error) {
      console.error('Failed to fetch deployments:', error);

      // Handle specific error types for better user experience
      if (error instanceof Error) {
        // Check for backend connectivity issues
        if (
          error.message.includes('Backend Unavailable') ||
          error.message.includes('Service Unavailable') ||
          error.message.includes('503')
        ) {
          console.warn(
            'Backend service is unavailable, returning empty deployments array',
          );
          // Return empty array instead of throwing error to allow app to continue
          return [];
        }

        // Check for connection failures
        if (
          error.message.includes('Failed to connect') ||
          error.message.includes('Connection refused') ||
          error.message.includes('ECONNREFUSED')
        ) {
          console.warn(
            'Backend connection failed, returning empty deployments array',
          );
          return [];
        }
      }

      // For other errors, still throw to maintain error handling behavior
      throw error;
    }
  }

  /**
   * Fetch deployment filters configuration from the backend API
   */
  async getDeploymentFilters(): Promise<any> {
    try {
      const response = await apiClient.get<{ data: any }>(
        '/api/v1/machine-learning/deployments/config/filter',
        {
          skipCache: true, // Force skip cache for filter configuration data
        },
      );
      return response.data;
    } catch (error) {
      console.error('Failed to fetch deployment filters configuration:', error);

      // Handle specific error types for better user experience
      if (error instanceof Error) {
        // Check for backend connectivity issues
        if (
          error.message.includes('Backend Unavailable') ||
          error.message.includes('Service Unavailable') ||
          error.message.includes('503')
        ) {
          console.warn(
            'Backend service is unavailable for deployment filters, returning empty configuration',
          );
          // Return empty configuration instead of throwing error to allow app to continue
          return {};
        }

        // Check for connection failures
        if (
          error.message.includes('Failed to connect') ||
          error.message.includes('Connection refused') ||
          error.message.includes('ECONNREFUSED')
        ) {
          console.warn(
            'Backend connection failed for deployment filters, returning empty configuration',
          );
          return {};
        }
      }

      // For other errors, still throw to maintain error handling behavior
      throw error;
    }
  }

  /**
   * Fetch deployment destinations configuration from the backend API
   */
  async getDeploymentDestinations(): Promise<any> {
    try {
      const response = await apiClient.get<any>(
        '/api/v1/deployments/config/destinations',
        {
          skipCache: true, // Force skip cache for destinations configuration data
        },
      );
      return response;
    } catch (error) {
      console.error('Failed to fetch deployment destinations configuration:', error);

      // Handle specific error types for better user experience
      if (error instanceof Error) {
        // Check for backend connectivity issues
        if (
          error.message.includes('Backend Unavailable') ||
          error.message.includes('Service Unavailable') ||
          error.message.includes('503')
        ) {
          console.warn(
            'Backend service is unavailable for deployment destinations, returning empty configuration',
          );
          // Return empty configuration instead of throwing error to allow app to continue
          return [];
        }

        // Check for connection failures
        if (
          error.message.includes('Failed to connect') ||
          error.message.includes('Connection refused') ||
          error.message.includes('ECONNREFUSED')
        ) {
          console.warn(
            'Backend connection failed for deployment destinations, returning empty configuration',
          );
          return [];
        }
      }

      // For other errors, still throw to maintain error handling behavior
      throw error;
    }
  }

  /**
   * Filter deployments by name
   */
  filterByName(deployments: Deployment[], name: string): Deployment[] {
    return deployments.filter((deployment) => deployment.name === name);
  }

  /**
   * Filter deployments by namespace
   */
  filterByNamespace(
    deployments: Deployment[],
    namespace: string,
  ): Deployment[] {
    return deployments.filter(
      (deployment) => deployment.namespace === namespace,
    );
  }

  /**
   * Get unique namespaces from deployments
   */
  getNamespaces(deployments: Deployment[]): string[] {
    const namespaces = new Set(
      deployments.map((deployment) => deployment.namespace),
    );
    return Array.from(namespaces).sort();
  }

  /**
   * Get unique deployment names
   */
  getDeploymentNames(deployments: Deployment[]): string[] {
    const names = new Set(deployments.map((deployment) => deployment.name));
    return Array.from(names).sort();
  }

  /**
   * Get deployment data for a specific time range
   */
  getDataInTimeRange(
    deployment: Deployment,
    startTime: number,
    endTime: number,
  ): { [timestamp: string]: number } {
    const result: { [timestamp: string]: number } = {};

    Object.entries(deployment.data).forEach(([timestamp, value]) => {
      const time = parseInt(timestamp);
      if (time >= startTime && time <= endTime) {
        result[timestamp] = value;
      }
    });

    return result;
  }

  /**
   * Get the latest timestamp and value for a deployment
   */
  getLatestDataPoint(
    deployment: Deployment,
  ): { timestamp: number; value: number } | null {
    const timestamps = Object.keys(deployment.data)
      .map((ts) => parseInt(ts))
      .sort((a, b) => b - a); // Sort descending

    if (timestamps.length === 0) {
      return null;
    }

    const latestTimestamp = timestamps[0];
    return {
      timestamp: latestTimestamp,
      value: deployment.data[latestTimestamp.toString()],
    };
  }

  /**
   * Calculate statistics for a deployment's data
   */
  getDeploymentStats(deployment: Deployment): {
    min: number;
    max: number;
    avg: number;
    count: number;
    sum: number;
  } {
    const values = Object.values(deployment.data);

    if (values.length === 0) {
      return { min: 0, max: 0, avg: 0, count: 0, sum: 0 };
    }

    const min = Math.min(...values);
    const max = Math.max(...values);
    const sum = values.reduce((acc, val) => acc + val, 0);
    const avg = sum / values.length;

    return { min, max, avg, count: values.length, sum };
  }

  /**
   * Refresh WebSocket connection with updated configuration
   * This should be called when configuration changes
   */
  async refreshConnectionWithNewConfig(): Promise<void> {
    console.log('[DeploymentsService] Refreshing connection with new config');

    // If there's an active connection, disconnect first
    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
      console.log(
        '[DeploymentsService] Disconnecting existing connection for config refresh',
      );

      // Store current subscribers to reconnect them
      const currentSubscribers = new Map(this.subscribers);

      this.disconnectWebSocket();

      // Reconnect with new config after a short delay
      setTimeout(async () => {
        if (currentSubscribers.size > 0) {
          console.log('[DeploymentsService] Reconnecting with new config');
          try {
            // Reconnect each subscriber
            for (const [subscriptionId, subscriber] of currentSubscribers) {
              this.subscribers.set(subscriptionId, subscriber);
            }

            // Re-establish connection
            this.shouldReconnect = true;
            this.establishConnection();
          } catch (error) {
            console.error(
              '[DeploymentsService] Failed to reconnect with new config:',
              error,
            );
          }
        }
      }, 100);
    }
  }
}

// Singleton instance
export const deploymentsService = new DeploymentsService();
