import ThreatHuntsRouter from './ThreatHuntsRouter';
import {
  Request,
  Response,
  NextFunction,
  Router,
  Router as ExpressRouter,
} from 'express';
import ThreatsController from '../controllers/ThreatsController';
import SystemMetricsController from '../controllers/SystemMetricsController';
import MachineLearningController from '../controllers/MachineLearningController';
 
const router: ExpressRouter = Router();
 
// Create a sub-router for threats API
const threatsRouter: ExpressRouter = Router();
const threatsController = new ThreatsController(threatsRouter);
 
// Create a sub-router for system metrics API
const systemMetricsRouter: ExpressRouter = Router();
const systemMetricsController = new SystemMetricsController(
  systemMetricsRouter,
);
 
// Create a sub-router for machine learning API
const mlRouter: ExpressRouter = Router();
const mlController = new MachineLearningController(mlRouter);
 
// Mount the routers at the API path
router.use('/api/v1', threatsRouter);
router.use('/api/v1', systemMetricsRouter);
router.use('/api/v1', mlRouter);
router.use('/api/v1', ThreatHuntsRouter);
 
router.use('*name', (req: Request, res: Response, _next: NextFunction) => {
  // Middleware to handle all requests with a specific pattern
  console.log(`Request received at ${req.originalUrl}`);
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString(),
  });
});
 
// Export controller instances for cleanup during shutdown
export { threatsController, systemMetricsController, mlController };
export default router;