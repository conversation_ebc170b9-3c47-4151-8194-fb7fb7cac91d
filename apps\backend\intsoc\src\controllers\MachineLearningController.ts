import { Router as ExpressRouter, Request, Response } from 'express';
import fetch from 'node-fetch';
import { MachineLearningCache } from '../services/MachineLearningCache';
import {
  MachineLearningService,
  CreateDeploymentRequest,
  DeleteDeploymentRequest,
} from '../services/MachineLearningService';
import UnifiedWebSocketService from '../services/UnifiedWebSocketService';
import config from '../config';
 
export default class MachineLearningController {
  private router: ExpressRouter;
  private mlCache: MachineLearningCache;
  private mlService: MachineLearningService;
  private unifiedWebSocketService: UnifiedWebSocketService;
 
  constructor(router: ExpressRouter) {
    this.router = router;
 
    // Initialize ML cache with Redis configuration
    this.mlCache = new MachineLearningCache();
 
    // Initialize ML service for WebSocket updates
    this.mlService = MachineLearningService.getInstance({
      mlCache: this.mlCache,
      cacheTimeout: 3600, // 1 hour
    });
 
    // Initialize unified WebSocket service
    this.unifiedWebSocketService = UnifiedWebSocketService.getInstance();
    this.unifiedWebSocketService.setMachineLearningCache(this.mlCache);
 
    this.initializeRoutes();
    this.initializeServices();
  }
 
  private async initializeServices(): Promise<void> {
    try {
      await this.mlCache.initialize();
      console.log(
        'MachineLearningController: ML cache initialized successfully',
      );
 
      await this.mlService.initialize();
      console.log(
        'MachineLearningController: ML service initialized successfully',
      );
    } catch (error) {
      console.error(
        'MachineLearningController: Failed to initialize services:',
        error,
      );
      throw error;
    }
  }
 
  initializeRoutes(): void {
    this.router.get(
      '/machine-learning/deployments',
      async (req: Request, res: Response): Promise<void> => {
        try {
          // First, check if we have valid (non-expired) cached data
          let hasValidCache = false;
          let cachedData = null;
 
          try {
            hasValidCache = await this.mlCache.hasValidMachineLearningData();
            if (hasValidCache) {
              cachedData = await this.mlCache.getMachineLearningData();
            }
          } catch (cacheError) {
            console.warn(
              'MachineLearningController: Cache check failed, falling back to HTTP:',
              cacheError,
            );
            hasValidCache = false;
            cachedData = null;
          }
 
          if (hasValidCache && cachedData) {
            // Ensure all deployments (with all fields) are returned
            res.status(200).json({
              data: cachedData,
              source: 'cache',
              timestamp: new Date().toISOString(),
            });
            return;
          }
 
          // Fallback to HTTP request if no valid cached data is available
          const response = await fetch(config.externalApi.mlApiUrl);
 
          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }
 
          const mlData = await response.json();
 
          // Try to cache the HTTP response for future requests
          let cached = false;
          try {
            cached = await this.mlCache.storeMachineLearningData(mlData, 300); // 5 minutes TTL
 
            // Only broadcast if caching succeeded to ensure consistency
            if (cached) {
              try {
                this.unifiedWebSocketService.broadcastMachineLearning(
                  mlData,
                  'api',
                );
              } catch (wsError) {
                console.warn(
                  'MachineLearningController: Failed to broadcast ML data:',
                  wsError,
                );
              }
            }
          } catch (cacheError) {
            console.warn(
              'MachineLearningController: Failed to cache HTTP response:',
              cacheError,
            );
            cached = false;
          }
 
          // Return all deployments (with all fields) from the API/cached data
          res.status(200).json({
            data: mlData,
            source: 'api',
            cached: cached,
            timestamp: new Date().toISOString(),
          });
        } catch (error) {
          res.status(500).json({
            error: 'Failed to fetch machine learning data',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );
 
    // Comprehensive health check endpoint
    this.router.get(
      '/machine-learning/health',
      async (req: Request, res: Response): Promise<void> => {
        try {
          // Cache health check and status
          const cacheStatus = this.mlCache.getConnectionStatus();
          const isCacheHealthy = await this.mlCache.healthCheck();
          const hasCachedData = await this.mlCache.hasMachineLearningData();
          const hasValidCachedData =
            await this.mlCache.hasValidMachineLearningData();
 
          // WebSocket service status and statistics
          const wsStatus = this.mlService.getConnectionStatus();
          const isWsConnected = this.mlService.isConnected();
          const mlStats = this.mlService.getStats();
 
          // Cache configuration
          const cacheTimeout = this.mlService.getCacheTimeout();
 
          // WebSocket connection count
          const wsConnectionCount =
            this.unifiedWebSocketService.getConnectionCount('ml');
 
          // Test external ML API connectivity
          const apiConnectivity = await this.mlService.testConnectivity();
 
          // Determine overall health status
          const overallStatus =
            cacheStatus === 'connected' && isCacheHealthy && isWsConnected && apiConnectivity.success
              ? 'healthy'
              : cacheStatus === 'connected' || isWsConnected || apiConnectivity.success
                ? 'degraded'
                : 'unhealthy';
 
          const httpStatus = overallStatus === 'unhealthy' ? 503 : 200;
 
          res.status(httpStatus).json({
            status: overallStatus,
            services: {
              cache: {
                status: cacheStatus,
                healthy: isCacheHealthy,
                hasData: hasCachedData,
                hasValidData: hasValidCachedData,
                message: isCacheHealthy
                  ? 'Cache service is healthy'
                  : 'Cache service is unhealthy',
              },
              webSocket: {
                status: wsStatus,
                connected: isWsConnected,
                stats: mlStats,
              },
              mlWebSocket: {
                connections: wsConnectionCount,
                status: wsConnectionCount > 0 ? 'active' : 'idle',
              },
              externalApi: {
                status: apiConnectivity.success ? 'connected' : 'disconnected',
                url: apiConnectivity.url,
                message: apiConnectivity.message,
                healthy: apiConnectivity.success
              }
            },
            configuration: {
              cacheTimeout: cacheTimeout,
            },
            timestamp: new Date().toISOString(),
          });
        } catch (error) {
          res.status(500).json({
            status: 'unhealthy',
            error: error instanceof Error ? error.message : 'Unknown error',
            message: 'Health check failed',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );
 
    // Create new deployment endpoint
    // POST /machine-learning/deployments
    // Request body: { "type": "anomaly" | "dga", "name": "deployment display name" }
    // Response: { "message": "...", "data": {...}, "namespace": "uuid", "timestamp": "..." }
    this.router.post(
      '/machine-learning/deployments',
      async (req: Request, res: Response): Promise<void> => {
        try {
          // Accept all fields from the frontend form
          const { type, name, sourceField, filterType, filterValue, COUNT_TYPE } = req.body;
 
          if (!type || !name) {
            res.status(400).json({
              error: 'Bad Request',
              message: 'Both "type" and "name" fields are required',
              timestamp: new Date().toISOString(),
            });
            return;
          }
 
          if (type !== 'anomaly' && type !== 'dga') {
            res.status(400).json({
              error: 'Bad Request',
              message: 'Type must be either "anomaly" or "dga"',
              timestamp: new Date().toISOString(),
            });
            return;
          }
 
          // Pass all fields to the ML service
          const createRequest: CreateDeploymentRequest = {
            type: type as 'anomaly' | 'dga',
            name: name,
            sourceField: sourceField || '',
            filterType: filterType || '',
            filterValue: filterValue || '',
            COUNT_TYPE: COUNT_TYPE || '',
          };
 
          console.log(
            'MachineLearningController: Creating new deployment',
            createRequest,
          );
 
          // Call the ML service to create the deployment
          const result = await this.mlService.createDeployment(createRequest);
 
          if (result.success) {
            res.status(201).json({
              message: 'Deployment created successfully',
              data: result.data,
              namespace: result.namespace,
              // Return all fields for frontend display
              deployment: createRequest,
              timestamp: new Date().toISOString(),
            });
          } else {
            res.status(500).json({
              error: 'Failed to create deployment',
              message: result.error,
              timestamp: new Date().toISOString(),
            });
          }
        } catch (error) {
          console.error(
            'MachineLearningController: Error creating deployment:',
            error,
          );
 
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );
 
    // Delete deployments endpoint
    this.router.delete(
      '/machine-learning/deployments',
      async (req: Request, res: Response): Promise<void> => {
        try {
          // Validate request body
          const { deployments } = req.body;
 
          if (!deployments || !Array.isArray(deployments)) {
            res.status(400).json({
              error: 'Bad Request',
              message: 'Request body must contain a "deployments" array',
              timestamp: new Date().toISOString(),
            });
            return;
          }
 
          if (deployments.length === 0) {
            res.status(400).json({
              error: 'Bad Request',
              message: 'Deployments array cannot be empty',
              timestamp: new Date().toISOString(),
            });
            return;
          }
 
          for (const [index, deployment] of deployments.entries()) {
            if (!deployment.type || !deployment.namespace) {
              res.status(400).json({
                error: 'Bad Request',
                message: `Deployment at index ${index} is missing required fields "type" and/or "namespace"`,
                timestamp: new Date().toISOString(),
              });
              return;
            }

            const isValidType = deployment.type === 'anomaly' ||
                               deployment.type === 'dga' ||
                               deployment.type.startsWith('anomaly-') ||
                               deployment.type.startsWith('dga-');
            
            if (!isValidType) {
              res.status(400).json({
                error: 'Bad Request',
                message: `Deployment at index ${index} has invalid type "${deployment.type}". Must be either "anomaly", "dga", or start with "anomaly-" or "dga-"`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }
 
          const deleteRequest: DeleteDeploymentRequest = {
            deployments: deployments.map(
              (d: { type: string; namespace: string }) => ({
                type: d.type as 'anomaly' | 'dga',
                namespace: d.namespace,
              }),
            ),
          };
 
          console.log(
            'MachineLearningController: Deleting deployments',
            deleteRequest,
          );
 
          // Call the ML service to delete the deployments
          const result = await this.mlService.deleteDeployments(deleteRequest);
 
          if (result.success) {
            const httpStatus =
              result.errors && result.errors.length > 0 ? 207 : 200; // 207 for partial success
            res.status(httpStatus).json({
              message: result.message,
              deletedCount: result.deletedCount,
              errors: result.errors,
              timestamp: new Date().toISOString(),
            });
          } else {
            res.status(500).json({
              error: 'Failed to delete deployments',
              message: result.message,
              errors: result.errors,
              timestamp: new Date().toISOString(),
            });
          }
        } catch (error) {
          console.error(
            'MachineLearningController: Error deleting deployments:',
            error,
          );
 
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );
 
    // Get deployment filters configuration endpoint
    // GET /machine-learning/deployments/config/filter
    // Response: filter configuration data from external service
    this.router.get(
      '/machine-learning/deployments/config/filter',
      async (req: Request, res: Response): Promise<void> => {
        try {
          console.log(
            'MachineLearningController: Fetching deployment filters configuration from external API',
          );
 
          // Build the URL for the external filter configuration service
          const filterUrl = `${config.externalApi.mlApiUrl}/config/filter`;
 
          console.log(
            `MachineLearningController: Requesting filter config from: ${filterUrl}`,
          );
 
          // Make the request to the external service
          const response = await fetch(filterUrl);
 
          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }
 
          const filterData = await response.json();
 
          console.log(
            'MachineLearningController: Successfully fetched deployment filters configuration',
          );
 
          res.status(200).json({
            data: filterData,
            source: 'external-api',
            timestamp: new Date().toISOString(),
          });
        } catch (error) {
          console.error(
            'MachineLearningController: Error fetching deployment filters configuration:',
            error,
          );
 
          // Handle specific error cases
          if (error instanceof Error) {
            const isConnectivityIssue =
              error.message.includes('ECONNREFUSED') ||
              error.message.includes('Failed to connect') ||
              error.message.includes('fetch failed');
 
            if (isConnectivityIssue) {
              res.status(503).json({
                error: 'Service Unavailable',
                message:
                  'External filter configuration service is temporarily unavailable',
                timestamp: new Date().toISOString(),
              });
              return;
            }
 
            if (error.message.includes('External API returned status:')) {
              const statusMatch = error.message.match(/status: (\d+)/);
              const status = statusMatch ? parseInt(statusMatch[1]) : 500;
 
              res.status(status).json({
                error: 'External API Error',
                message: `Filter configuration service returned error: ${error.message}`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }
 
          // Generic error response
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );

    // Get deployment destinations configuration endpoint
    // GET /machine-learning/deployments/config/destinations
    // Response: destinations configuration data from external service
    this.router.get(
      '/machine-learning/deployments/config/destinations',
      async (req: Request, res: Response): Promise<void> => {
        try {
          console.log(
            'MachineLearningController: Fetching deployment destinations configuration from external API',
          );

          // Build the URL for the external destinations configuration service
          const destinationsUrl = `${config.externalApi.mlConfigApiUrl}/destinations`;

          console.log(
            `MachineLearningController: Requesting destinations config from: ${destinationsUrl}`,
          );

          // Make the request to the external service
          const response = await fetch(destinationsUrl);

          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }

          const destinationsData = await response.json();

          console.log(
            'MachineLearningController: Successfully fetched deployment destinations configuration',
          );

          res.status(200).json(destinationsData);
        } catch (error) {
          console.error(
            'MachineLearningController: Error fetching deployment destinations configuration:',
            error,
          );

          // Handle specific error cases
          if (error instanceof Error) {
            const isConnectivityIssue =
              error.message.includes('ECONNREFUSED') ||
              error.message.includes('Failed to connect') ||
              error.message.includes('fetch failed');

            if (isConnectivityIssue) {
              res.status(503).json({
                error: 'Service Unavailable',
                message:
                  'External destinations configuration service is temporarily unavailable',
                timestamp: new Date().toISOString(),
              });
              return;
            }

            if (error.message.includes('External API returned status:')) {
              const statusMatch = error.message.match(/status: (\d+)/);
              const status = statusMatch ? parseInt(statusMatch[1]) : 500;

              res.status(status).json({
                error: 'External API Error',
                message: `Destinations configuration service returned error: ${error.message}`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }

          // Generic error response
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );

    // Get deployment destinations configuration endpoint (direct path)
    // GET /deployments/config/destinations
    // Response: destinations configuration data from external service
    this.router.get(
      '/deployments/config/destinations',
      async (req: Request, res: Response): Promise<void> => {
        try {
          console.log(
            'MachineLearningController: Fetching deployment destinations configuration from external API (direct path)',
          );

          // Build the URL for the external destinations configuration service
          const destinationsUrl = `${config.externalApi.mlConfigApiUrl}/destinations`;

          console.log(
            `MachineLearningController: Requesting destinations config from: ${destinationsUrl}`,
          );

          // Make the request to the external service
          const response = await fetch(destinationsUrl);

          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }

          const destinationsData = await response.json();

          console.log(
            'MachineLearningController: Successfully fetched deployment destinations configuration (direct path)',
          );

          res.status(200).json(destinationsData);
        } catch (error) {
          console.error(
            'MachineLearningController: Error fetching deployment destinations configuration (direct path):',
            error,
          );

          // Handle specific error cases
          if (error instanceof Error) {
            const isConnectivityIssue =
              error.message.includes('ECONNREFUSED') ||
              error.message.includes('Failed to connect') ||
              error.message.includes('fetch failed');

            if (isConnectivityIssue) {
              res.status(503).json({
                error: 'Service Unavailable',
                message:
                  'External destinations configuration service is temporarily unavailable',
                timestamp: new Date().toISOString(),
              });
              return;
            }

            if (error.message.includes('External API returned status:')) {
              const statusMatch = error.message.match(/status: (\d+)/);
              const status = statusMatch ? parseInt(statusMatch[1]) : 500;

              res.status(status).json({
                error: 'External API Error',
                message: `Destinations configuration service returned error: ${error.message}`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }

          // Generic error response
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );

    // Get specific deployment configuration endpoint
    // GET /machine-learning/deployments/config?name=<name>&namespace=<namespace>
    // Response: detailed configuration data for specific deployment
    this.router.get(
      '/machine-learning/deployments/config',
      async (req: Request, res: Response): Promise<void> => {
        try {
          const { name, namespace } = req.query;

          if (!name || !namespace) {
            res.status(400).json({
              error: 'Bad Request',
              message: 'Both name and namespace parameters are required',
              timestamp: new Date().toISOString(),
            });
            return;
          }

          console.log(
            `MachineLearningController: Fetching deployment configuration for ${name}@${namespace}`,
          );

          // Build the URL for the external configuration service
          const configUrl = `${config.externalApi.mlConfigApiUrl}?name=${encodeURIComponent(name as string)}&namespace=${encodeURIComponent(namespace as string)}`;

          console.log(
            `MachineLearningController: Requesting config from: ${configUrl}`,
          );

          // Make the request to the external service
          const response = await fetch(configUrl);

          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }

          const configData = await response.json();

          console.log(
            'MachineLearningController: Successfully fetched deployment configuration',
          );

          res.status(200).json(configData);
        } catch (error) {
          console.error(
            'MachineLearningController: Error fetching deployment configuration:',
            error,
          );

          // Handle specific error cases
          if (error instanceof Error) {
            const isConnectivityIssue =
              error.message.includes('ECONNREFUSED') ||
              error.message.includes('Failed to connect') ||
              error.message.includes('fetch failed');

            if (isConnectivityIssue) {
              res.status(503).json({
                error: 'Service Unavailable',
                message:
                  'External configuration service is temporarily unavailable',
                timestamp: new Date().toISOString(),
              });
              return;
            }

            if (error.message.includes('External API returned status:')) {
              const statusMatch = error.message.match(/status: (\d+)/);
              const status = statusMatch ? parseInt(statusMatch[1]) : 500;

              res.status(status).json({
                error: 'External API Error',
                message: `Configuration service returned error: ${error.message}`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }

          // Generic error response
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );

    // Get specific deployment configuration endpoint (direct path)
    // GET /deployments/config?name=<name>&namespace=<namespace>
    // Response: detailed configuration data for specific deployment
    this.router.get(
      '/deployments/config',
      async (req: Request, res: Response): Promise<void> => {
        try {
          const { name, namespace } = req.query;

          if (!name || !namespace) {
            res.status(400).json({
              error: 'Bad Request',
              message: 'Both name and namespace parameters are required',
              timestamp: new Date().toISOString(),
            });
            return;
          }

          console.log(
            `MachineLearningController: Fetching deployment configuration for ${name}@${namespace} (direct path)`,
          );

          // Build the URL for the external configuration service
          const configUrl = `${config.externalApi.mlConfigApiUrl}?name=${encodeURIComponent(name as string)}&namespace=${encodeURIComponent(namespace as string)}`;

          console.log(
            `MachineLearningController: Requesting config from: ${configUrl}`,
          );

          // Make the request to the external service
          const response = await fetch(configUrl);

          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }

          const configData = await response.json();

          console.log(
            'MachineLearningController: Successfully fetched deployment configuration (direct path)',
          );

          res.status(200).json(configData);
        } catch (error) {
          console.error(
            'MachineLearningController: Error fetching deployment configuration (direct path):',
            error,
          );

          // Handle specific error cases
          if (error instanceof Error) {
            const isConnectivityIssue =
              error.message.includes('ECONNREFUSED') ||
              error.message.includes('Failed to connect') ||
              error.message.includes('fetch failed');

            if (isConnectivityIssue) {
              res.status(503).json({
                error: 'Service Unavailable',
                message:
                  'External configuration service is temporarily unavailable',
                timestamp: new Date().toISOString(),
              });
              return;
            }

            if (error.message.includes('External API returned status:')) {
              const statusMatch = error.message.match(/status: (\d+)/);
              const status = statusMatch ? parseInt(statusMatch[1]) : 500;

              res.status(status).json({
                error: 'External API Error',
                message: `Configuration service returned error: ${error.message}`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }

          // Generic error response
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );

    // Update deployment configuration endpoint
    // POST /deployments/config
    // Body: configuration data to update
    this.router.post(
      '/deployments/config',
      async (req: Request, res: Response): Promise<void> => {
        try {
          console.log(
            'MachineLearningController: Updating deployment configuration',
            req.body,
          );

          // Forward the request to the external configuration service
          const configUrl = `${config.externalApi.mlConfigApiUrl}`;

          const response = await fetch(configUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(req.body),
          });

          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }

          const result = await response.json();

          console.log(
            'MachineLearningController: Successfully updated deployment configuration',
          );

          res.status(200).json(result);
        } catch (error) {
          console.error(
            'MachineLearningController: Error updating deployment configuration:',
            error,
          );

          // Handle specific error cases
          if (error instanceof Error) {
            const isConnectivityIssue =
              error.message.includes('ECONNREFUSED') ||
              error.message.includes('Failed to connect') ||
              error.message.includes('fetch failed');

            if (isConnectivityIssue) {
              res.status(503).json({
                error: 'Service Unavailable',
                message:
                  'External configuration service is temporarily unavailable',
                timestamp: new Date().toISOString(),
              });
              return;
            }

            if (error.message.includes('External API returned status:')) {
              const statusMatch = error.message.match(/status: (\d+)/);
              const status = statusMatch ? parseInt(statusMatch[1]) : 500;

              res.status(status).json({
                error: 'External API Error',
                message: `Configuration service returned error: ${error.message}`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }

          // Generic error response
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );

    // Update deployment destinations configuration endpoint
    // POST /deployments/config/destinations
    // Body: {"destinations": ["IRIS"], "config": {"IRIS_API_KEY": "KEY_HERE", "IRIS_HOST": "https://************"}}
    this.router.post(
      '/deployments/config/destinations',
      async (req: Request, res: Response): Promise<void> => {
        try {
          console.log(
            'MachineLearningController: Updating deployment destinations configuration',
            req.body,
          );

          // Forward the request to the external destinations configuration service
          const destinationsUrl = `${config.externalApi.mlConfigApiUrl}/destinations`;

          const response = await fetch(destinationsUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(req.body),
          });

          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }

          const result = await response.json();

          console.log(
            'MachineLearningController: Successfully updated deployment destinations configuration',
          );

          res.status(200).json(result);
        } catch (error) {
          console.error(
            'MachineLearningController: Error updating deployment destinations configuration:',
            error,
          );

          // Handle specific error cases
          if (error instanceof Error) {
            const isConnectivityIssue =
              error.message.includes('ECONNREFUSED') ||
              error.message.includes('Failed to connect') ||
              error.message.includes('fetch failed');

            if (isConnectivityIssue) {
              res.status(503).json({
                error: 'Service Unavailable',
                message:
                  'External destinations configuration service is temporarily unavailable',
                timestamp: new Date().toISOString(),
              });
              return;
            }

            if (error.message.includes('External API returned status:')) {
              const statusMatch = error.message.match(/status: (\d+)/);
              const status = statusMatch ? parseInt(statusMatch[1]) : 500;

              res.status(status).json({
                error: 'External API Error',
                message: `Destinations configuration service returned error: ${error.message}`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }

          // Generic error response
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );
  }
 
  /**
   * Cleanup method to properly dispose of resources
   */
  public cleanup(): void {
    console.log('MachineLearningController: Cleaning up resources');
 
    try {
      // Cleanup ML service
      this.mlService.cleanup();
 
      // Cleanup ML cache
      this.mlCache.cleanup();
 
      console.log('MachineLearningController: Cleanup completed successfully');
    } catch (error) {
      console.error('MachineLearningController: Error during cleanup:', error);
    }
  }
}
