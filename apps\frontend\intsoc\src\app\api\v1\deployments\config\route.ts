/**
 * Next.js API Route: /api/v1/deployments/config
 * Proxies requests to the backend deployment config endpoint with query parameters
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  backendApiProxy,
  BackendApiError,
} from '../../../../../lib/backend-proxy';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const name = searchParams.get('name');
    const namespace = searchParams.get('namespace');

    console.log(
      `[API Route] GET /api/v1/deployments/config - name: ${name}, namespace: ${namespace}`,
    );

    // Build query string for backend request
    const queryString = new URLSearchParams();
    if (name) queryString.append('name', name);
    if (namespace) queryString.append('namespace', namespace);

    const backendPath = `/api/v1/deployments/config${queryString.toString() ? `?${queryString.toString()}` : ''}`;

    // Forward the request to the backend with proper error handling
    let response;
    try {
      response = await backendApiProxy.forwardRequest(backendPath, {
        method: 'GET',
      });
    } catch (proxyError: unknown) {
      console.error('[API Route] Backend proxy error:', proxyError);
      
      if (proxyError instanceof BackendApiError) {
        return NextResponse.json(
          {
            error: 'Backend API Error',
            message: proxyError.message,
            timestamp: new Date().toISOString(),
          },
          { status: proxyError.status || 500 },
        );
      }

      return NextResponse.json(
        {
          error: 'Proxy Error',
          message: 'Failed to connect to backend service',
          timestamp: new Date().toISOString(),
        },
        { status: 503 },
      );
    }

    // Parse the response data
    let data;
    try {
      data = await response.json();
    } catch (parseError) {
      console.error('[API Route] Failed to parse backend response:', parseError);
      return NextResponse.json(
        {
          error: 'Parse Error',
          message: 'Invalid response format from backend',
          timestamp: new Date().toISOString(),
        },
        { status: 502 },
      );
    }

    console.log(`[API Route] Backend response: status=${response.status}`);

    // Return the successful response
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
      },
    });
  } catch (error) {
    console.error('[API Route] GET deployment config error:', error);

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'Failed to fetch deployment configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('[API Route] POST /api/v1/deployments/config - Updating configuration...');

    const body = await request.json();

    // Forward the request to the backend
    const response = await backendApiProxy.forwardRequest(
      '/api/v1/deployments/config',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      },
    );

    const responseData = await response.json();

    return NextResponse.json(responseData, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
      },
    });
  } catch (error) {
    console.error('[API Route] POST deployment config error:', error);

    if (error instanceof BackendApiError) {
      return NextResponse.json(
        {
          error: 'Backend API Error',
          message: error.message,
          timestamp: new Date().toISOString(),
        },
        { status: error.status || 500 },
      );
    }

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'Failed to update deployment configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
