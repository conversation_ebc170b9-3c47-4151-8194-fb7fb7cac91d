/**
 * Next.js API Route: /api/v1/deployments/config/destinations
 * Proxies requests to the backend deployment destinations endpoint
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  backendApiProxy,
  BackendApiError,
} from '../../../../../../lib/backend-proxy';

export async function GET(_request: NextRequest) {
  try {
    console.log(
      '[API Route] GET /api/v1/deployments/config/destinations - Proxying to backend...',
    );

    // Forward the request to the backend with proper error handling
    let response;
    try {
      response = await backendApiProxy.forwardRequest(
        '/api/v1/deployments/config/destinations',
        {
          method: 'GET',
        },
      );
    } catch (proxyError: unknown) {
      console.error('[API Route] Backend proxy error:', proxyError);
      
      if (proxyError instanceof BackendApiError) {
        return NextResponse.json(
          {
            error: 'Backend API Error',
            message: proxyError.message,
            timestamp: new Date().toISOString(),
          },
          { status: proxyError.status || 500 },
        );
      }

      // For non-BackendApiError instances, return a generic error
      return NextResponse.json(
        {
          error: 'Proxy Error',
          message: 'Failed to connect to backend service',
          timestamp: new Date().toISOString(),
        },
        { status: 503 },
      );
    }

    // Parse the response data
    let data;
    try {
      data = await response.json();
    } catch (parseError) {
      console.error('[API Route] Failed to parse backend response:', parseError);
      return NextResponse.json(
        {
          error: 'Parse Error',
          message: 'Invalid response format from backend',
          timestamp: new Date().toISOString(),
        },
        { status: 502 },
      );
    }

    console.log(`[API Route] Backend response: status=${response.status}`);

    // Return the successful response
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
      },
    });
  } catch (error) {
    console.error('[API Route] GET deployment destinations error:', error);

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'Failed to fetch deployment destinations configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * Health check endpoint for deployment destinations
 */
export async function HEAD(_request: NextRequest) {
  try {
    // Perform a lightweight check to the backend
    const response = await backendApiProxy.forwardRequest(
      '/api/v1/deployments/config/destinations',
      {
        method: 'HEAD',
      },
    );

    return new NextResponse(null, {
      status: response.status,
      headers: {
        'X-Health-Check': 'ok',
        'X-Backend-Status': response.status.toString(),
      },
    });
  } catch (error) {
    console.error('[API Route] Health check failed:', error);

    return new NextResponse(null, {
      status: 503,
      headers: {
        'X-Health-Check': 'failed',
        'X-Backend-Status': 'unavailable',
      },
    });
  }
}

/**
 * Update deployment destinations configuration
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[API Route] POST /api/v1/deployments/config/destinations - Updating configuration...');

    const body = await request.json();

    // Forward the request to the backend
    const response = await backendApiProxy.forwardRequest(
      '/api/v1/deployments/config/destinations',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      },
    );

    const responseData = await response.json();

    console.log(`[API Route] Backend POST response: status=${response.status}`);

    return NextResponse.json(responseData, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
      },
    });
  } catch (error) {
    console.error('[API Route] POST deployment destinations error:', error);

    if (error instanceof BackendApiError) {
      return NextResponse.json(
        {
          error: 'Backend API Error',
          message: error.message,
          timestamp: new Date().toISOString(),
        },
        { status: error.status || 500 },
      );
    }

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'Failed to update deployment destinations configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
